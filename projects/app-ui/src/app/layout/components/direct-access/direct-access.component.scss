:host {
  display: block;
  position: relative;
}

.direct-access-container {
  max-width: 400px;
  width: 100%;
  transition: max-width var(--transition-normal);
  display: flex;
  align-items: center;
  position: relative;

  &:focus-within {
    max-width: 450px;
  }

  .input-container {
    width: 100%;
  }

  .direct-access-field {
    width: 100%;
    font-size: var(--font-size-md);
    margin: 0;
    height: 42px;

    ::ng-deep .mat-mdc-form-field-flex {
      background-color: rgba(255, 255, 255, 0.15);
      border-radius: var(--border-radius-full);
      transition: background-color var(--transition-fast);

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }
    }

    ::ng-deep .mat-mdc-form-field-infix {
      padding: 8px;
      min-height: unset;
    }

    ::ng-deep .mat-mdc-text-field-wrapper {
      padding: 0 0 0 var(--spacing-md);
      background-color: transparent;
    }

    ::ng-deep .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }

    ::ng-deep .mdc-notched-outline__leading,
    ::ng-deep .mdc-notched-outline__notch,
    ::ng-deep .mdc-notched-outline__trailing {
      border-color: transparent !important;
    }

    input {
      color: var(--text-on-primary);
      caret-color: var(--text-on-primary);
      height: 28px;
      line-height: 28px;

      &::placeholder {
        color: rgba(255, 255, 255, 0.7);
      }
    }

    button {
      color: var(--text-on-primary);
      line-height: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      mat-icon {
        filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
      }
    }
  }

  // 聊天面板样式
  .chat-panel {
    position: absolute;
    top: 50px;
    left: 0;
    width: 350px;
    max-height: 500px;
    background-color: var(--background-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    display: flex;
    flex-direction: column;
    z-index: 1000;
    overflow: hidden;
    animation: fadeIn 0.2s ease-in-out;

    .chat-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-sm) var(--spacing-md);
      background-color: var(--primary-color);
      color: var(--text-on-primary);

      h3 {
        margin: 0;
        font-size: var(--font-size-md);
        font-weight: 500;
      }

      button {
        color: var(--text-on-primary);
      }
    }

    .chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: var(--spacing-md);
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
      max-height: 350px;

      .message {
        display: flex;
        margin-bottom: var(--spacing-sm);

        .message-content {
          padding: var(--spacing-sm) var(--spacing-md);
          border-radius: var(--border-radius-lg);
          max-width: 80%;

          p {
            margin: 0;
            white-space: pre-line;
          }

          &.loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-sm) var(--spacing-lg);

            .dot {
              width: 8px;
              height: 8px;
              background-color: var(--text-secondary-color);
              border-radius: 50%;
              margin: 0 2px;
              animation: bounce 1.5s infinite ease-in-out;

              &:nth-child(1) {
                animation-delay: 0s;
              }

              &:nth-child(2) {
                animation-delay: 0.2s;
              }

              &:nth-child(3) {
                animation-delay: 0.4s;
              }
            }
          }
        }

        &.user-message {
          justify-content: flex-end;

          .message-content {
            background-color: var(--primary-light-color);
            color: var(--text-on-primary);
          }
        }

        &.system-message {
          justify-content: flex-start;

          .message-content {
            background-color: var(--background-alt-color);
            color: var(--text-primary-color);
          }
        }

        .message-links {
          display: flex;
          flex-wrap: wrap;
          gap: var(--spacing-xs);
          margin-top: var(--spacing-sm);

          a {
            margin: 0;
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: var(--font-size-sm);
            line-height: 1;
            white-space: nowrap;
          }
        }
      }
    }

    .chat-input {
      padding: var(--spacing-sm);
      border-top: 1px solid var(--border-color);

      .chat-field {
        width: 100%;
        margin: 0;

        ::ng-deep .mat-mdc-form-field-subscript-wrapper {
          display: none;
        }
      }
    }
  }
}

// 移动设备样式
.mobile-direct-access-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.5);
  animation: fadeIn var(--transition-fast) forwards;

  .mobile-direct-access-field {
    width: 100%;
    margin: 0;
    height: 42px;

    ::ng-deep .mat-mdc-form-field-flex {
      background-color: rgba(255, 255, 255, 0.15);
      border-radius: var(--border-radius-full);
      transition: background-color var(--transition-fast);

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }
    }

    ::ng-deep .mat-mdc-form-field-infix {
      padding: 8px 1.5em;
      min-height: unset;
    }

    ::ng-deep .mat-mdc-text-field-wrapper {
      padding: 0 0 0 var(--spacing-md);
      background-color: transparent;
    }

    input {
      color: var(--text-on-primary);
      caret-color: var(--text-on-primary);
      height: 28px;
      line-height: 28px;

      &::placeholder {
        color: rgba(255, 255, 255, 0.7);
      }
    }

    button {
      color: var(--text-on-primary);
      height: 42px;
      width: 42px;
      line-height: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      mat-icon {
        filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
      }
    }

    ::ng-deep .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }

    ::ng-deep .mdc-notched-outline__leading,
    ::ng-deep .mdc-notched-outline__notch,
    ::ng-deep .mdc-notched-outline__trailing {
      border-color: transparent !important;
    }
  }

  // 移动端聊天面板
  .mobile-chat-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
    height: 80%;
    max-height: 600px;
    background-color: var(--background-color);
    border-radius: var(--border-radius-lg);
    display: flex;
    flex-direction: column;
    z-index: 1001;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    animation: zoomIn 0.2s ease-in-out;

    .chat-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-sm) var(--spacing-md);
      background-color: var(--primary-color);
      color: var(--text-on-primary);

      h3 {
        margin: 0;
        font-size: var(--font-size-md);
        font-weight: 500;
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-xs);
      }

      button {
        color: var(--text-on-primary);
      }
    }

    .chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: var(--spacing-md);
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);

      // 与桌面版相同的消息样式
      .message {
        display: flex;
        margin-bottom: var(--spacing-sm);

        .message-content {
          padding: var(--spacing-sm) var(--spacing-md);
          border-radius: var(--border-radius-lg);
          max-width: 80%;

          p {
            margin: 0;
            white-space: pre-line;
          }

          &.loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-sm) var(--spacing-lg);

            .dot {
              width: 8px;
              height: 8px;
              background-color: var(--text-secondary-color);
              border-radius: 50%;
              margin: 0 2px;
              animation: bounce 1.5s infinite ease-in-out;

              &:nth-child(1) {
                animation-delay: 0s;
              }

              &:nth-child(2) {
                animation-delay: 0.2s;
              }

              &:nth-child(3) {
                animation-delay: 0.4s;
              }
            }
          }
        }

        &.user-message {
          justify-content: flex-end;

          .message-content {
            background-color: var(--primary-light-color);
            color: var(--text-on-primary);
          }
        }

        &.system-message {
          justify-content: flex-start;

          .message-content {
            background-color: var(--background-alt-color);
            color: var(--text-primary-color);
          }
        }

        .message-links {
          display: flex;
          flex-wrap: wrap;
          gap: var(--spacing-xs);
          margin-top: var(--spacing-sm);

          a {
            margin: 0;
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: var(--font-size-sm);
            line-height: 1;
            white-space: nowrap;
          }
        }
      }
    }

    .chat-input {
      padding: var(--spacing-md);
      border-top: 1px solid var(--border-color);
      background-color: var(--background-color);

      .chat-field {
        width: 100%;
        margin: 0;

        ::ng-deep .mat-mdc-form-field-flex {
          background-color: var(--background-alt-color);
          border-radius: var(--border-radius-full);
        }

        ::ng-deep .mat-mdc-form-field-infix {
          padding: 8px 1.5em;
        }

        ::ng-deep .mat-mdc-text-field-wrapper {
          padding: 0 0 0 var(--spacing-md);
        }

        ::ng-deep .mat-mdc-form-field-subscript-wrapper {
          display: none;
        }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}
