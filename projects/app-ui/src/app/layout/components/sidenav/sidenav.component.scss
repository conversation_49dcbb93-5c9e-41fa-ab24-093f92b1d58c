:host {
  display: block;
}

.mat-toolbar {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  font-weight: var(--md-sys-typescale-font-weight-medium);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  height: var(--md-sys-layout-header-height);
  display: flex;
  align-items: center;
  padding: 0 var(--md-sys-spacing-4);
  font-size: var(--md-sys-typescale-title-medium-size);
}

.mat-nav-list {
  padding: var(--md-sys-spacing-4) var(--md-sys-spacing-3);

  a {
    border-radius: 0;
    height: 48px;
    font-weight: var(--md-sys-typescale-font-weight-regular);
    transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
    display: flex;
    align-items: center;
    padding: 0 var(--md-sys-spacing-4);
    color: var(--md-sys-color-on-surface);
    position: relative;
    overflow: hidden;

    mat-icon {
      margin-right: var(--md-sys-spacing-4);
      width: var(--md-sys-layout-icon-size-medium);
      height: var(--md-sys-layout-icon-size-medium);
      color: var(--md-sys-color-on-surface-variant);
      transition: color var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
    }

    &:hover {
      background-color: var(--md-sys-color-surface-variant);

      mat-icon {
        color: var(--md-sys-color-primary);
      }
    }

    &.active {
      background-color: rgba(var(--md-sys-color-primary-rgb), 0.08);
      color: var(--md-sys-color-primary);
      font-weight: var(--md-sys-typescale-font-weight-medium);
      border-left: 3px solid var(--md-sys-color-primary);

      mat-icon {
        color: var(--md-sys-color-primary);
      }
    }
  }
}

.mat-divider {
  margin: var(--spacing-md) var(--spacing-sm);
  border-color: var(--divider-color);
}
