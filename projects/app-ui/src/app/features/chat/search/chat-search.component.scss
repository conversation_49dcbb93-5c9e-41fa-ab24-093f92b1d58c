// 使用新的设计系统样式
.search-container {
  width: 100%;
  max-width: 400px;
  margin: 0 var(--md-sys-spacing-4);

  @media (max-width: 768px) {
    margin: 0 var(--md-sys-spacing-2);
  }
}

.search-field {
  width: 100%;
  font-size: var(--md-sys-typescale-body-small-size);

  // 简化的 Material 覆盖
  ::ng-deep {
    .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }

    .mat-mdc-form-field-infix {
      padding: var(--md-sys-spacing-2) 0;
      min-height: unset;
    }

    .mat-mdc-text-field-wrapper {
      background-color: var(--md-sys-color-surface);
      border-radius: var(--md-sys-shape-corner-large);
      padding: 0 var(--md-sys-spacing-2);
      border: 1px solid var(--md-sys-color-outline);
      transition: border-color var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);

      &:hover {
        border-color: var(--md-sys-color-primary);
      }

      &.mdc-text-field--focused {
        border-color: var(--md-sys-color-primary);
        box-shadow: 0 0 0 1px rgba(var(--md-sys-color-primary-rgb), 0.2);
      }
    }

    .mat-mdc-form-field-flex {
      align-items: center;
      height: 36px;
    }

    .mdc-notched-outline {
      display: none;
    }
  }
}

.search-prefix {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--md-sys-spacing-2);
  color: var(--md-sys-color-primary);

  mat-icon {
    font-size: var(--md-sys-layout-icon-size-medium);
    width: var(--md-sys-layout-icon-size-medium);
    height: var(--md-sys-layout-icon-size-medium);
  }

  @media (max-width: 768px) {
    display: none;
  }
}

.search-input {
  color: var(--md-sys-color-on-surface);
  caret-color: var(--md-sys-color-primary);

  &::placeholder {
    color: var(--md-sys-color-on-surface-variant);
    opacity: 1;
  }

  @media (max-width: 768px) {
    padding-left: var(--md-sys-spacing-2);
  }
}

.clear-button {
  color: var(--md-sys-color-on-surface-variant);
  width: 24px;
  height: 24px;
  line-height: 24px;

  &:hover {
    color: var(--md-sys-color-primary);
  }

  mat-icon {
    font-size: var(--md-sys-layout-icon-size-small);
    width: var(--md-sys-layout-icon-size-small);
    height: var(--md-sys-layout-icon-size-small);
  }
}

::ng-deep .mat-mdc-autocomplete-panel {
  border-radius: var(--md-sys-shape-corner-medium);
  margin-top: var(--md-sys-spacing-2);
  background-color: var(--md-sys-color-surface);
  box-shadow: var(--md-sys-elevation-level3);
  border: 1px solid var(--md-sys-color-outline-variant);
  overflow: hidden;
  padding: var(--md-sys-spacing-2) 0;
  min-width: 300px;
}

.search-result-option {
  height: auto;
  line-height: var(--md-sys-typescale-line-height-normal);
  padding: var(--md-sys-spacing-3) var(--md-sys-spacing-4);
  transition: background-color var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);

  &:hover {
    background-color: rgba(var(--md-sys-color-primary-rgb), 0.08);
  }
}

.search-result-content {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-2);
}

.search-result-session {
  font-weight: var(--md-sys-typescale-font-weight-medium);
  color: var(--md-sys-color-on-surface);
  font-size: var(--md-sys-typescale-body-small-size);
  display: flex;
  align-items: center;

  &::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: var(--md-sys-shape-corner-full);
    background-color: var(--md-sys-color-primary);
    margin-right: var(--md-sys-spacing-2);
    opacity: 0.7;
  }
}

.search-result-message {
  color: var(--md-sys-color-on-surface-variant);
  font-size: var(--md-sys-typescale-label-large-size);
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-left: var(--md-sys-spacing-4);

  ::ng-deep mark {
    background-color: var(--md-sys-color-warning-container);
    color: var(--md-sys-color-on-warning-container);
    padding: 0 var(--md-sys-spacing-1);
    border-radius: var(--md-sys-shape-corner-extra-small);
    font-weight: var(--md-sys-typescale-font-weight-medium);
  }
}

.status-option {
  height: 48px;
  line-height: 48px;
}

.status-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--md-sys-spacing-3);
  color: var(--md-sys-color-on-surface-variant);
  font-size: var(--md-sys-typescale-body-small-size);

  mat-icon {
    opacity: 0.7;
    font-size: var(--md-sys-layout-icon-size-small);
    width: var(--md-sys-layout-icon-size-small);
    height: var(--md-sys-layout-icon-size-small);
  }
}

.loading-spinner {
  width: var(--md-sys-layout-icon-size-small);
  height: var(--md-sys-layout-icon-size-small);
  border-radius: var(--md-sys-shape-corner-full);
  border: 2px solid rgba(var(--md-sys-color-primary-rgb), 0.3);
  border-top-color: var(--md-sys-color-primary);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
