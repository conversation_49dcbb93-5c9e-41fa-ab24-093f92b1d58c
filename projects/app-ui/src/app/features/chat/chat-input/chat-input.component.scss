.chat-input-container {
  display: flex;
  flex-direction: column;
  padding: var(--md-sys-spacing-2) var(--md-sys-spacing-3) var(--md-sys-spacing-2) 0;
  border-top: none;
  box-shadow: none;
  gap: var(--md-sys-spacing-2);

  @media (max-width: 768px) {
    padding: var(--md-sys-spacing-1);
    gap: var(--md-sys-spacing-1);
  }
}

// 编辑状态指示器样式
.editing-indicator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--md-sys-spacing-1) var(--md-sys-spacing-3);
  background-color: rgba(var(--md-sys-color-primary-rgb), 0.04);
  border-radius: var(--md-sys-shape-corner-small);
  margin-bottom: var(--md-sys-spacing-1);

  span {
    font-size: var(--md-sys-typescale-body-small-size);
    color: var(--md-sys-color-primary);
  }

  button {
    min-width: auto;
    padding: 0 var(--md-sys-spacing-2);
    font-size: var(--md-sys-typescale-body-small-size);
    line-height: 28px;
  }
}

// 生成状态指示器样式
.generating-indicator {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: rgba(var(--primary-rgb), 0.05);
  border-radius: 4px;
  margin-bottom: 8px;

  span {
    font-size: 14px;
    color: var(--primary-color);
    margin-left: 12px;
  }

  .pulse-dots {
    display: flex;
    align-items: center;

    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: var(--primary-color);
      margin-right: 4px;
      animation: pulse 1.5s infinite;

      &:nth-child(2) {
        animation-delay: 0.3s;
      }

      &:nth-child(3) {
        animation-delay: 0.6s;
      }
    }
  }
}

.message-input {
  flex: 1;

  /* 使用类选择器而不是 ::ng-deep */
  &.mat-mdc-form-field {
    /* 设置基本样式 - 微信风格 */
    display: block;
    background-color: var(--mdc-filled-text-field-container-color, #ffffff);
    border-radius: var(--mdc-filled-text-field-container-shape, 4px);
    box-shadow: none;
    border: 1px solid var(--mdc-filled-text-field-hover-active-indicator-color, #d9d9d9);
    transition: none;

    /* 聚焦时的样式 */
    &.mat-focused {
      box-shadow: none;
      border-color: var(--mdc-filled-text-field-focus-active-indicator-color, var(--primary-color));
    }

    /* 禁用状态的样式 */
    &.disabled {
      background-color: rgba(0, 0, 0, 0.04);
      border-color: rgba(0, 0, 0, 0.12);
      opacity: 0.8;
      cursor: not-allowed;

      /* 禁用状态下的提示文本样式 */
      .mat-mdc-form-field-hint {
        color: rgba(0, 0, 0, 0.38);
      }
    }
  }
}

// 输入区域和操作按钮容器
.input-actions-container {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 10px;
}

// 语音输入和添加按钮容器
.action-buttons-container {
  text-align: center;
  >.send-button {
    margin-bottom: var(--spacing-xs);
  }
  > .icon-buttons {
    display: flex;
    flex-wrap: nowrap;
  }
}

// 语音输入按钮
.mic-button {
  flex: none;

  &:hover:not(:disabled) {
    color: var(--primary-color);
    background-color: rgba(var(--primary-rgb), 0.05);
  }

  &.active {
    color: white;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.3);
    transform: scale(1.1);
    animation: pulse 1.5s infinite;

    // 添加内部波纹动画
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 70%);
      transform: scale(0);
      animation: ripple 2s linear infinite;
    }

    // 添加麦克风图标的特效
    mat-icon {
      animation: bounce 0.8s ease infinite alternate;
    }
  }

  &:disabled {
    opacity: var(--mat-primary-button-disabled-opacity, 0.5);
  }
}

// 加号按钮
.add-button {
  flex: none;

  &:hover:not(:disabled) {
    color: var(--primary-color);
    background-color: rgba(var(--primary-rgb), 0.05);
  }

  &:disabled {
    opacity: var(--mat-primary-button-disabled-opacity, 0.5);
  }
}

// 语音输入按钮的动画
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0.6);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(var(--primary-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0);
  }
}

// 波纹动画
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(3);
    opacity: 0;
  }
}

// 麦克风图标的弹跳动画
@keyframes bounce {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.2);
  }
}

// 加号菜单样式
::ng-deep .add-menu {
  .mat-mdc-menu-content {
    padding: var(--spacing-xs, 8px) 0;
  }

  .mat-mdc-menu-item {
    min-height: 44px;
    line-height: 44px;
    font-size: var(--font-size-sm, 15px);
  }

  mat-icon {
    margin-right: var(--spacing-sm, 12px);
    vertical-align: middle;
    font-size: var(--font-size-lg, 20px);
  }

  span {
    vertical-align: middle;
  }
}

// 发送按钮
.send-button {
  align-self: center; // 微信的发送按钮是垂直居中的
  flex-shrink: 0;
  background-color: var(--mat-primary-button-background-color, var(--primary-color)); // 使用主题变量
  color: var(--mat-primary-button-text-color, white);
  transition: none;
  min-width: 60px;
  height: 36px;
  margin-top: 0;
  border-radius: var(--border-radius-sm, 4px);
  font-weight: var(--font-weight-regular, 400);
  font-size: var(--font-size-sm, 14px);
  text-transform: none;
  letter-spacing: normal;
  padding: 0 16px;
  white-space: nowrap;
  box-shadow: none;

  @media (max-width: 600px) {
    min-width: 50px;
    padding: 0 12px;
  }

  &:hover:not(:disabled) {
    background-color: var(--mat-primary-darker, var(--primary-dark));
  }

  &:disabled {
    background-color: var(--mat-primary-button-disabled-background-color, var(--primary-color));
    opacity: var(--mat-primary-button-disabled-opacity, 0.5);
    cursor: not-allowed;

    // 当助理正在生成消息时的特殊样式
    &.generating {
      background-color: rgba(0, 0, 0, 0.12);
      color: rgba(0, 0, 0, 0.38);
      border: 1px solid rgba(0, 0, 0, 0.12);
    }
  }
}

// 上传进度条容器
.upload-progress-container {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 4px 0;
  gap: 8px;

  mat-progress-bar {
    flex-grow: 1;
  }

  .upload-progress-text {
    font-size: 12px;
    color: var(--text-secondary);
    min-width: 40px;
    text-align: right;
  }
}

// 附件预览区
.attachments-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
  margin-bottom: 8px;

  .attachment-item {
    display: flex;
    align-items: center;
    background-color: rgba(var(--primary-rgb), 0.1);
    border-radius: 4px;
    padding: 4px 8px;
    max-width: 100%;

    .attachment-name {
      font-size: 14px;
      color: var(--primary-color);
      margin-right: 8px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 150px;
    }

    .attachment-size {
      font-size: 12px;
      color: var(--text-secondary);
      margin-right: 4px;
    }

    .remove-attachment {
      width: 20px;
      height: 20px;
      line-height: 20px;
      min-width: auto;
      padding: 0;

      .remove-icon {
        font-size: 16px;
        color: var(--text-secondary);
      }

      &:hover .remove-icon {
        color: var(--error-color);
      }
    }
  }
}
