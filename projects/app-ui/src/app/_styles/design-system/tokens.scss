// 设计令牌 (Design Tokens)
// 定义所有基础的设计变量，遵循 Material Design 3 规范

:root {
  // ===== 颜色系统 =====
  // 主色调
  --md-sys-color-primary: #1976d2;
  --md-sys-color-primary-container: #e3f2fd;
  --md-sys-color-on-primary: #ffffff;
  --md-sys-color-on-primary-container: #004ba0;
  
  // 次要色调
  --md-sys-color-secondary: #63a4ff;
  --md-sys-color-secondary-container: #e1f5fe;
  --md-sys-color-on-secondary: #ffffff;
  --md-sys-color-on-secondary-container: #0277bd;
  
  // 表面色调
  --md-sys-color-surface: #ffffff;
  --md-sys-color-surface-variant: #f5f9fc;
  --md-sys-color-on-surface: #212121;
  --md-sys-color-on-surface-variant: #757575;
  
  // 背景色调
  --md-sys-color-background: #f5f9fc;
  --md-sys-color-on-background: #212121;
  
  // 错误色调
  --md-sys-color-error: #b00020;
  --md-sys-color-error-container: #fdeaea;
  --md-sys-color-on-error: #ffffff;
  --md-sys-color-on-error-container: #410002;
  
  // 成功色调
  --md-sys-color-success: #4caf50;
  --md-sys-color-success-container: #e8f5e8;
  --md-sys-color-on-success: #ffffff;
  --md-sys-color-on-success-container: #1b5e20;
  
  // 警告色调
  --md-sys-color-warning: #ff9100;
  --md-sys-color-warning-container: #fff3e0;
  --md-sys-color-on-warning: #ffffff;
  --md-sys-color-on-warning-container: #e65100;
  
  // 轮廓色调
  --md-sys-color-outline: #e0e0e0;
  --md-sys-color-outline-variant: #f5f5f5;
  
  // 阴影色调
  --md-sys-color-shadow: rgba(0, 0, 0, 0.2);
  --md-sys-color-scrim: rgba(0, 0, 0, 0.32);
  
  // ===== RGB 值（用于 rgba 函数）=====
  --md-sys-color-primary-rgb: 25, 118, 210;
  --md-sys-color-secondary-rgb: 99, 164, 255;
  --md-sys-color-surface-rgb: 255, 255, 255;
  --md-sys-color-error-rgb: 176, 0, 32;
  --md-sys-color-success-rgb: 76, 175, 80;
  --md-sys-color-warning-rgb: 255, 145, 0;
  
  // ===== 字体系统 =====
  // 字体族
  --md-sys-typescale-font-family-base: "Microsoft YaHei", "Microsoft JhengHei", "SimHei", "SimSun", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "WenQuanYi Micro Hei", "Noto Sans CJK SC", "Source Han Sans SC", -apple-system, BlinkMacSystemFont, sans-serif;
  --md-sys-typescale-font-family-heading: var(--md-sys-typescale-font-family-base);
  --md-sys-typescale-font-family-monospace: Consolas, "Microsoft YaHei Mono", "Microsoft JhengHei Mono", "SimSun-ExtB", "Courier New", "Liberation Mono", "Noto Sans Mono CJK SC", "Source Han Mono SC", monospace;
  
  // 字体大小
  --md-sys-typescale-display-large-size: 3.5rem;    // 56px
  --md-sys-typescale-display-medium-size: 2.8125rem; // 45px
  --md-sys-typescale-display-small-size: 2.25rem;   // 36px
  --md-sys-typescale-headline-large-size: 2rem;     // 32px
  --md-sys-typescale-headline-medium-size: 1.75rem; // 28px
  --md-sys-typescale-headline-small-size: 1.5rem;   // 24px
  --md-sys-typescale-title-large-size: 1.375rem;    // 22px
  --md-sys-typescale-title-medium-size: 1.125rem;   // 18px
  --md-sys-typescale-title-small-size: 1rem;        // 16px
  --md-sys-typescale-label-large-size: 0.875rem;    // 14px
  --md-sys-typescale-label-medium-size: 0.75rem;    // 12px
  --md-sys-typescale-label-small-size: 0.6875rem;   // 11px
  --md-sys-typescale-body-large-size: 1.125rem;     // 18px
  --md-sys-typescale-body-medium-size: 1rem;        // 16px
  --md-sys-typescale-body-small-size: 0.875rem;     // 14px
  
  // 字体粗细
  --md-sys-typescale-font-weight-light: 300;
  --md-sys-typescale-font-weight-regular: 400;
  --md-sys-typescale-font-weight-medium: 500;
  --md-sys-typescale-font-weight-semibold: 600;
  --md-sys-typescale-font-weight-bold: 700;
  
  // 行高
  --md-sys-typescale-line-height-tight: 1.2;
  --md-sys-typescale-line-height-normal: 1.5;
  --md-sys-typescale-line-height-loose: 1.8;
  
  // ===== 间距系统 =====
  --md-sys-spacing-0: 0;
  --md-sys-spacing-1: 0.25rem;  // 4px
  --md-sys-spacing-2: 0.5rem;   // 8px
  --md-sys-spacing-3: 0.75rem;  // 12px
  --md-sys-spacing-4: 1rem;     // 16px
  --md-sys-spacing-5: 1.25rem;  // 20px
  --md-sys-spacing-6: 1.5rem;   // 24px
  --md-sys-spacing-8: 2rem;     // 32px
  --md-sys-spacing-10: 2.5rem;  // 40px
  --md-sys-spacing-12: 3rem;    // 48px
  --md-sys-spacing-16: 4rem;    // 64px
  --md-sys-spacing-20: 5rem;    // 80px
  --md-sys-spacing-24: 6rem;    // 96px
  
  // ===== 形状系统 =====
  --md-sys-shape-corner-none: 0;
  --md-sys-shape-corner-extra-small: 0.25rem;  // 4px
  --md-sys-shape-corner-small: 0.5rem;         // 8px
  --md-sys-shape-corner-medium: 0.75rem;       // 12px
  --md-sys-shape-corner-large: 1rem;           // 16px
  --md-sys-shape-corner-extra-large: 1.75rem;  // 28px
  --md-sys-shape-corner-full: 50%;
  
  // ===== 阴影系统 =====
  --md-sys-elevation-level0: none;
  --md-sys-elevation-level1: 0 1px 2px 0 rgba(0, 0, 0, 0.3), 0 1px 3px 1px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level2: 0 1px 2px 0 rgba(0, 0, 0, 0.3), 0 2px 6px 2px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level3: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 4px 8px 3px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level4: 0 2px 3px 0 rgba(0, 0, 0, 0.3), 0 6px 10px 4px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level5: 0 4px 4px 0 rgba(0, 0, 0, 0.3), 0 8px 12px 6px rgba(0, 0, 0, 0.15);
  
  // ===== 动画系统 =====
  --md-sys-motion-duration-short1: 50ms;
  --md-sys-motion-duration-short2: 100ms;
  --md-sys-motion-duration-short3: 150ms;
  --md-sys-motion-duration-short4: 200ms;
  --md-sys-motion-duration-medium1: 250ms;
  --md-sys-motion-duration-medium2: 300ms;
  --md-sys-motion-duration-medium3: 350ms;
  --md-sys-motion-duration-medium4: 400ms;
  --md-sys-motion-duration-long1: 450ms;
  --md-sys-motion-duration-long2: 500ms;
  --md-sys-motion-duration-long3: 550ms;
  --md-sys-motion-duration-long4: 600ms;
  
  --md-sys-motion-easing-linear: linear;
  --md-sys-motion-easing-standard: cubic-bezier(0.2, 0, 0, 1);
  --md-sys-motion-easing-standard-accelerate: cubic-bezier(0.3, 0, 1, 1);
  --md-sys-motion-easing-standard-decelerate: cubic-bezier(0, 0, 0, 1);
  --md-sys-motion-easing-emphasized: cubic-bezier(0.2, 0, 0, 1);
  --md-sys-motion-easing-emphasized-accelerate: cubic-bezier(0.3, 0, 0.8, 0.15);
  --md-sys-motion-easing-emphasized-decelerate: cubic-bezier(0.05, 0.7, 0.1, 1);
  
  // ===== Z-index 层级 =====
  --md-sys-z-index-dropdown: 1000;
  --md-sys-z-index-sticky: 1020;
  --md-sys-z-index-fixed: 1030;
  --md-sys-z-index-modal-backdrop: 1040;
  --md-sys-z-index-modal: 1050;
  --md-sys-z-index-popover: 1060;
  --md-sys-z-index-tooltip: 1070;
  
  // ===== 布局系统 =====
  --md-sys-layout-header-height: 64px;
  --md-sys-layout-sidebar-width: 260px;
  --md-sys-layout-content-max-width: 1200px;
  --md-sys-layout-avatar-size: 40px;
  --md-sys-layout-icon-size-small: 16px;
  --md-sys-layout-icon-size-medium: 24px;
  --md-sys-layout-icon-size-large: 32px;
}

// ===== 响应式断点 =====
@media (max-width: 599px) {
  :root {
    --md-sys-layout-header-height: 48px;
    --md-sys-layout-sidebar-width: 85%;
  }
}

@media (max-width: 768px) {
  :root {
    --md-sys-layout-sidebar-width: 280px;
  }
}

// ===== 暗色主题 =====
.dark-theme {
  // 颜色系统覆盖
  --md-sys-color-surface: #1e1e1e;
  --md-sys-color-surface-variant: #333333;
  --md-sys-color-on-surface: #eeeeee;
  --md-sys-color-on-surface-variant: #aaaaaa;
  --md-sys-color-background: #121212;
  --md-sys-color-on-background: #eeeeee;
  --md-sys-color-outline: #444444;
  --md-sys-color-outline-variant: #333333;
  
  // RGB 值覆盖
  --md-sys-color-surface-rgb: 30, 30, 30;
}
