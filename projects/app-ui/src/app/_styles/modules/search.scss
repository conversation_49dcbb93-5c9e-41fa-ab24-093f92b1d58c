// 搜索模块样式
// 定义搜索相关的样式

// ===== 搜索容器 =====
.search-container {
  position: relative;
  width: 100%;
  max-width: 600px;
}

// ===== 搜索输入框 =====
.search-input {
  width: 100%;
  padding: var(--md-sys-spacing-3) var(--md-sys-spacing-4);
  padding-left: var(--md-sys-spacing-12);
  border: 1px solid var(--md-sys-color-outline);
  border-radius: var(--md-sys-shape-corner-large);
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  font-family: var(--md-sys-typescale-font-family-base);
  font-size: var(--md-sys-typescale-body-medium-size);
  transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
  
  &::placeholder {
    color: var(--md-sys-color-on-surface-variant);
  }
  
  &:focus {
    outline: none;
    border-color: var(--md-sys-color-primary);
    box-shadow: 0 0 0 2px rgba(var(--md-sys-color-primary-rgb), 0.2);
  }
  
  &:hover:not(:focus) {
    border-color: var(--md-sys-color-on-surface-variant);
  }
}

// ===== 搜索图标 =====
.search-icon {
  position: absolute;
  left: var(--md-sys-spacing-4);
  top: 50%;
  transform: translateY(-50%);
  color: var(--md-sys-color-on-surface-variant);
  pointer-events: none;
  
  mat-icon {
    font-size: var(--md-sys-layout-icon-size-medium);
    width: var(--md-sys-layout-icon-size-medium);
    height: var(--md-sys-layout-icon-size-medium);
  }
}

// ===== 清除按钮 =====
.search-clear {
  position: absolute;
  right: var(--md-sys-spacing-2);
  top: 50%;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  border-radius: var(--md-sys-shape-corner-full);
  color: var(--md-sys-color-on-surface-variant);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
  
  &:hover {
    background-color: var(--md-sys-color-surface-variant);
    color: var(--md-sys-color-on-surface);
  }
  
  mat-icon {
    font-size: var(--md-sys-layout-icon-size-small);
    width: var(--md-sys-layout-icon-size-small);
    height: var(--md-sys-layout-icon-size-small);
  }
}

// ===== 搜索建议下拉框 =====
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--md-sys-color-surface);
  border: 1px solid var(--md-sys-color-outline-variant);
  border-radius: var(--md-sys-shape-corner-medium);
  box-shadow: var(--md-sys-elevation-level3);
  z-index: var(--md-sys-z-index-dropdown);
  max-height: 400px;
  overflow-y: auto;
  margin-top: var(--md-sys-spacing-1);
  
  .suggestion-item {
    padding: var(--md-sys-spacing-3) var(--md-sys-spacing-4);
    cursor: pointer;
    border-bottom: 1px solid var(--md-sys-color-outline-variant);
    transition: background-color var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
    
    &:last-child {
      border-bottom: none;
    }
    
    &:hover,
    &.highlighted {
      background-color: var(--md-sys-color-primary-container);
      color: var(--md-sys-color-on-primary-container);
    }
    
    .suggestion-text {
      font-size: var(--md-sys-typescale-body-medium-size);
      color: var(--md-sys-color-on-surface);
      margin-bottom: var(--md-sys-spacing-1);
    }
    
    .suggestion-meta {
      font-size: var(--md-sys-typescale-body-small-size);
      color: var(--md-sys-color-on-surface-variant);
    }
  }
  
  .no-suggestions {
    padding: var(--md-sys-spacing-4);
    text-align: center;
    color: var(--md-sys-color-on-surface-variant);
    font-size: var(--md-sys-typescale-body-small-size);
    font-style: italic;
  }
}

// ===== 搜索结果 =====
.search-results {
  margin-top: var(--md-sys-spacing-4);
  
  .results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--md-sys-spacing-4);
    padding-bottom: var(--md-sys-spacing-2);
    border-bottom: 1px solid var(--md-sys-color-outline-variant);
    
    .results-count {
      font-size: var(--md-sys-typescale-body-small-size);
      color: var(--md-sys-color-on-surface-variant);
    }
    
    .results-sort {
      display: flex;
      align-items: center;
      gap: var(--md-sys-spacing-2);
      
      select {
        padding: var(--md-sys-spacing-1) var(--md-sys-spacing-2);
        border: 1px solid var(--md-sys-color-outline);
        border-radius: var(--md-sys-shape-corner-small);
        background-color: var(--md-sys-color-surface);
        color: var(--md-sys-color-on-surface);
        font-size: var(--md-sys-typescale-body-small-size);
      }
    }
  }
  
  .result-item {
    padding: var(--md-sys-spacing-4);
    border: 1px solid var(--md-sys-color-outline-variant);
    border-radius: var(--md-sys-shape-corner-medium);
    margin-bottom: var(--md-sys-spacing-3);
    background-color: var(--md-sys-color-surface);
    transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
    
    &:hover {
      box-shadow: var(--md-sys-elevation-level2);
      border-color: var(--md-sys-color-primary);
    }
    
    .result-title {
      font-size: var(--md-sys-typescale-title-medium-size);
      font-weight: var(--md-sys-typescale-font-weight-medium);
      color: var(--md-sys-color-primary);
      margin-bottom: var(--md-sys-spacing-2);
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
    
    .result-snippet {
      font-size: var(--md-sys-typescale-body-medium-size);
      color: var(--md-sys-color-on-surface);
      line-height: var(--md-sys-typescale-line-height-normal);
      margin-bottom: var(--md-sys-spacing-2);
    }
    
    .result-meta {
      display: flex;
      align-items: center;
      gap: var(--md-sys-spacing-4);
      font-size: var(--md-sys-typescale-body-small-size);
      color: var(--md-sys-color-on-surface-variant);
      
      .result-date,
      .result-source {
        display: flex;
        align-items: center;
        gap: var(--md-sys-spacing-1);
        
        mat-icon {
          font-size: var(--md-sys-layout-icon-size-small);
          width: var(--md-sys-layout-icon-size-small);
          height: var(--md-sys-layout-icon-size-small);
        }
      }
    }
  }
}

// ===== 搜索高亮 =====
.search-highlight {
  background-color: var(--md-sys-color-warning-container);
  color: var(--md-sys-color-on-warning-container);
  padding: 0 var(--md-sys-spacing-1);
  border-radius: var(--md-sys-shape-corner-extra-small);
  font-weight: var(--md-sys-typescale-font-weight-medium);
}

// ===== 搜索过滤器 =====
.search-filters {
  display: flex;
  flex-wrap: wrap;
  gap: var(--md-sys-spacing-2);
  margin-bottom: var(--md-sys-spacing-4);
  
  .filter-chip {
    display: inline-flex;
    align-items: center;
    gap: var(--md-sys-spacing-1);
    padding: var(--md-sys-spacing-1) var(--md-sys-spacing-3);
    background-color: var(--md-sys-color-surface-variant);
    color: var(--md-sys-color-on-surface-variant);
    border: 1px solid var(--md-sys-color-outline-variant);
    border-radius: var(--md-sys-shape-corner-large);
    font-size: var(--md-sys-typescale-label-large-size);
    cursor: pointer;
    transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
    
    &:hover {
      background-color: var(--md-sys-color-primary-container);
      color: var(--md-sys-color-on-primary-container);
      border-color: var(--md-sys-color-primary);
    }
    
    &.active {
      background-color: var(--md-sys-color-primary);
      color: var(--md-sys-color-on-primary);
      border-color: var(--md-sys-color-primary);
    }
    
    .remove-filter {
      background: none;
      border: none;
      color: inherit;
      cursor: pointer;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      
      mat-icon {
        font-size: var(--md-sys-layout-icon-size-small);
        width: var(--md-sys-layout-icon-size-small);
        height: var(--md-sys-layout-icon-size-small);
      }
    }
  }
}

// ===== 加载状态 =====
.search-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-8);
  
  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--md-sys-color-surface-variant);
    border-top: 3px solid var(--md-sys-color-primary);
    border-radius: var(--md-sys-shape-corner-full);
    animation: search-spin 1s linear infinite;
  }
  
  .loading-text {
    margin-left: var(--md-sys-spacing-3);
    color: var(--md-sys-color-on-surface-variant);
    font-size: var(--md-sys-typescale-body-medium-size);
  }
}

@keyframes search-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// ===== 空状态 =====
.search-empty {
  text-align: center;
  padding: var(--md-sys-spacing-8);
  
  .empty-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto var(--md-sys-spacing-4);
    color: var(--md-sys-color-on-surface-variant);
    
    mat-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
    }
  }
  
  .empty-title {
    font-size: var(--md-sys-typescale-title-large-size);
    color: var(--md-sys-color-on-surface);
    margin-bottom: var(--md-sys-spacing-2);
  }
  
  .empty-message {
    font-size: var(--md-sys-typescale-body-medium-size);
    color: var(--md-sys-color-on-surface-variant);
    max-width: 400px;
    margin: 0 auto;
  }
}

// ===== 响应式调整 =====
@media (max-width: 768px) {
  .search-container {
    max-width: none;
  }
  
  .search-input {
    padding: var(--md-sys-spacing-2) var(--md-sys-spacing-3);
    padding-left: var(--md-sys-spacing-10);
  }
  
  .search-icon {
    left: var(--md-sys-spacing-3);
  }
  
  .search-results .results-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--md-sys-spacing-2);
  }
  
  .search-filters {
    gap: var(--md-sys-spacing-1);
    
    .filter-chip {
      font-size: var(--md-sys-typescale-label-medium-size);
      padding: var(--md-sys-spacing-1) var(--md-sys-spacing-2);
    }
  }
}

@media (max-width: 599px) {
  .result-item {
    padding: var(--md-sys-spacing-3);
    
    .result-meta {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--md-sys-spacing-1);
    }
  }
}
