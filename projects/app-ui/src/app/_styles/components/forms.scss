// 表单组件样式
// 定义统一的表单控件样式

// ===== 表单容器 =====
.form {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-4);
}

.form-row {
  display: flex;
  gap: var(--md-sys-spacing-4);
  align-items: flex-start;
  
  .form-field {
    flex: 1;
  }
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-2);
}

// ===== 表单字段 =====
.form-field {
  position: relative;
  
  .form-label {
    display: block;
    font-size: var(--md-sys-typescale-body-medium-size);
    font-weight: var(--md-sys-typescale-font-weight-medium);
    color: var(--md-sys-color-on-surface);
    margin-bottom: var(--md-sys-spacing-2);
    
    &.required::after {
      content: ' *';
      color: var(--md-sys-color-error);
    }
  }
  
  .form-help {
    font-size: var(--md-sys-typescale-body-small-size);
    color: var(--md-sys-color-on-surface-variant);
    margin-top: var(--md-sys-spacing-1);
  }
  
  .form-error {
    font-size: var(--md-sys-typescale-body-small-size);
    color: var(--md-sys-color-error);
    margin-top: var(--md-sys-spacing-1);
    display: flex;
    align-items: center;
    gap: var(--md-sys-spacing-1);
    
    mat-icon {
      font-size: var(--md-sys-layout-icon-size-small);
      width: var(--md-sys-layout-icon-size-small);
      height: var(--md-sys-layout-icon-size-small);
    }
  }
}

// ===== 输入控件基础样式 =====
.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: var(--md-sys-spacing-3) var(--md-sys-spacing-4);
  border: 1px solid var(--md-sys-color-outline);
  border-radius: var(--md-sys-shape-corner-small);
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  font-family: var(--md-sys-typescale-font-family-base);
  font-size: var(--md-sys-typescale-body-medium-size);
  line-height: var(--md-sys-typescale-line-height-normal);
  transition: border-color var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
  
  &::placeholder {
    color: var(--md-sys-color-on-surface-variant);
  }
  
  &:focus {
    outline: none;
    border-color: var(--md-sys-color-primary);
    box-shadow: 0 0 0 1px var(--md-sys-color-primary);
  }
  
  &:disabled {
    background-color: var(--md-sys-color-surface-variant);
    color: var(--md-sys-color-on-surface-variant);
    cursor: not-allowed;
    opacity: 0.6;
  }
  
  &.error {
    border-color: var(--md-sys-color-error);
    
    &:focus {
      border-color: var(--md-sys-color-error);
      box-shadow: 0 0 0 1px var(--md-sys-color-error);
    }
  }
}

// ===== 文本区域 =====
.form-textarea {
  resize: vertical;
  min-height: 80px;
  font-family: var(--md-sys-typescale-font-family-base);
}

// ===== 选择框 =====
.form-select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--md-sys-spacing-3) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: var(--md-sys-spacing-10);
  
  &:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%231976d2' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  }
}

// ===== 复选框和单选按钮 =====
.form-checkbox,
.form-radio {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-2);
  cursor: pointer;
  
  input[type="checkbox"],
  input[type="radio"] {
    width: 20px;
    height: 20px;
    margin: 0;
    cursor: pointer;
    accent-color: var(--md-sys-color-primary);
  }
  
  .form-check-label {
    font-size: var(--md-sys-typescale-body-medium-size);
    color: var(--md-sys-color-on-surface);
    cursor: pointer;
  }
  
  &:hover input[type="checkbox"],
  &:hover input[type="radio"] {
    box-shadow: 0 0 0 8px rgba(var(--md-sys-color-primary-rgb), 0.1);
  }
}

// ===== 开关 =====
.form-switch {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-3);
  cursor: pointer;
  
  .switch-input {
    position: relative;
    width: 44px;
    height: 24px;
    
    input[type="checkbox"] {
      opacity: 0;
      width: 0;
      height: 0;
    }
    
    .switch-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: var(--md-sys-color-outline);
      transition: var(--md-sys-motion-duration-short2);
      border-radius: 24px;
      
      &:before {
        position: absolute;
        content: "";
        height: 20px;
        width: 20px;
        left: 2px;
        bottom: 2px;
        background-color: var(--md-sys-color-surface);
        transition: var(--md-sys-motion-duration-short2);
        border-radius: 50%;
      }
    }
    
    input:checked + .switch-slider {
      background-color: var(--md-sys-color-primary);
    }
    
    input:checked + .switch-slider:before {
      transform: translateX(20px);
    }
  }
  
  .switch-label {
    font-size: var(--md-sys-typescale-body-medium-size);
    color: var(--md-sys-color-on-surface);
    cursor: pointer;
  }
}

// ===== 文件上传 =====
.form-file {
  position: relative;
  
  .file-input {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .file-label {
    display: inline-flex;
    align-items: center;
    gap: var(--md-sys-spacing-2);
    padding: var(--md-sys-spacing-3) var(--md-sys-spacing-4);
    border: 2px dashed var(--md-sys-color-outline);
    border-radius: var(--md-sys-shape-corner-medium);
    background-color: var(--md-sys-color-surface);
    color: var(--md-sys-color-on-surface);
    cursor: pointer;
    transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
    
    &:hover {
      border-color: var(--md-sys-color-primary);
      background-color: rgba(var(--md-sys-color-primary-rgb), 0.04);
    }
    
    mat-icon {
      color: var(--md-sys-color-primary);
    }
  }
  
  &.has-file .file-label {
    border-style: solid;
    border-color: var(--md-sys-color-primary);
    background-color: rgba(var(--md-sys-color-primary-rgb), 0.08);
  }
}

// ===== 表单验证状态 =====
.form-field.success {
  .form-input,
  .form-textarea,
  .form-select {
    border-color: var(--md-sys-color-success);
    
    &:focus {
      border-color: var(--md-sys-color-success);
      box-shadow: 0 0 0 1px var(--md-sys-color-success);
    }
  }
  
  .form-success {
    font-size: var(--md-sys-typescale-body-small-size);
    color: var(--md-sys-color-success);
    margin-top: var(--md-sys-spacing-1);
    display: flex;
    align-items: center;
    gap: var(--md-sys-spacing-1);
    
    mat-icon {
      font-size: var(--md-sys-layout-icon-size-small);
      width: var(--md-sys-layout-icon-size-small);
      height: var(--md-sys-layout-icon-size-small);
    }
  }
}

// ===== 表单操作 =====
.form-actions {
  display: flex;
  gap: var(--md-sys-spacing-3);
  justify-content: flex-end;
  margin-top: var(--md-sys-spacing-6);
  
  &.form-actions-start {
    justify-content: flex-start;
  }
  
  &.form-actions-center {
    justify-content: center;
  }
  
  &.form-actions-between {
    justify-content: space-between;
  }
}

// ===== 响应式调整 =====
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: var(--md-sys-spacing-3);
  }
  
  .form-actions {
    flex-direction: column;
    
    .btn {
      width: 100%;
    }
  }
}

@media (max-width: 599px) {
  .form {
    gap: var(--md-sys-spacing-3);
  }
  
  .form-input,
  .form-textarea,
  .form-select {
    padding: var(--md-sys-spacing-2) var(--md-sys-spacing-3);
  }
}
