// Material Design 组件覆盖样式
// 只覆盖与标准 Material 组件有差异的必要样式

// ===== 按钮覆盖 =====
// 禁用按钮的透明度（Material 默认不够明显）
.mat-mdc-button:disabled,
.mat-mdc-raised-button:disabled,
.mat-mdc-outlined-button:disabled,
.mat-mdc-unelevated-button:disabled,
.mat-mdc-fab:disabled,
.mat-mdc-mini-fab:disabled {
  opacity: 0.5;
}

// ===== 对话框覆盖 =====
// 特殊的内容对话框（用于显示代码、图片等）
.content-dialog .mat-mdc-dialog-container {
  padding: 0;
  overflow: hidden;
}

// ===== 聊天输入框特殊样式 =====
// 聊天输入框需要特殊的无边框样式
.chat-input-field {
  .mat-mdc-form-field-focus-overlay {
    display: none;
  }

  .mdc-text-field--filled:not(.mdc-text-field--disabled) {
    background-color: transparent;
  }

  .mdc-line-ripple {
    display: none;
  }

  textarea {
    resize: none;
    padding: 0;
  }

  .mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input,
  .mdc-text-field--filled:not(.mdc-text-field--disabled):hover .mdc-text-field__input,
  .mdc-text-field--filled.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-text-field__input {
    border-bottom: none;
  }
}

// ===== 工具提示覆盖 =====
// 确保工具提示不会阻止鼠标事件
.mat-mdc-tooltip,
.mdc-tooltip__surface {
  pointer-events: none;
  user-select: none;
}

// ===== 特殊菜单样式 =====
// 加号菜单的特殊样式（更大的点击区域）
.add-menu .mat-mdc-menu-item {
  min-height: 44px;
}
