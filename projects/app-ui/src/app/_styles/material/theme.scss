// Material Design 主题配置
// 基于设计令牌配置 Angular Material 主题

@use '@angular/material' as mat;

// 包含核心样式
@include mat.core();

// 定义自定义主色调
$custom-primary: mat.m2-define-palette(mat.$m2-blue-palette, 600);
$custom-accent: mat.m2-define-palette(mat.$m2-blue-palette, A200);
$custom-warn: mat.m2-define-palette(mat.$m2-red-palette);

// 定义主题
$custom-theme: mat.m2-define-light-theme((
  color: (
    primary: $custom-primary,
    accent: $custom-accent,
    warn: $custom-warn,
  ),
  typography: mat.m2-define-typography-config(),
  density: 0,
));

// 应用主题
html {
  @include mat.all-component-themes($custom-theme);
}

// Material 组件的 CSS 变量映射
:root {
  // ===== 主色调映射 =====
  --mat-primary: var(--md-sys-color-primary);
  --mat-primary-rgb: var(--md-sys-color-primary-rgb);
  --mat-primary-contrast: var(--md-sys-color-on-primary);
  --mat-primary-darker: var(--md-sys-color-on-primary-container);
  --mat-primary-lighter: var(--md-sys-color-secondary);

  // ===== 按钮系统 =====
  // 主按钮
  --mat-filled-button-container-color: var(--md-sys-color-primary);
  --mat-filled-button-label-text-color: var(--md-sys-color-on-primary);
  --mat-filled-button-disabled-container-color: var(--md-sys-color-primary);
  --mat-filled-button-disabled-label-text-color: var(--md-sys-color-on-primary);
  --mat-filled-button-disabled-container-opacity: 0.5;

  // 轮廓按钮
  --mat-outlined-button-outline-color: var(--md-sys-color-outline);
  --mat-outlined-button-label-text-color: var(--md-sys-color-primary);
  --mat-outlined-button-disabled-outline-color: var(--md-sys-color-outline);
  --mat-outlined-button-disabled-label-text-color: var(--md-sys-color-on-surface-variant);

  // 文本按钮
  --mat-text-button-label-text-color: var(--md-sys-color-primary);
  --mat-text-button-disabled-label-text-color: var(--md-sys-color-on-surface-variant);

  // MDC 按钮变量
  --mdc-filled-button-container-color: var(--mat-filled-button-container-color);
  --mdc-filled-button-label-text-color: var(--mat-filled-button-label-text-color);
  --mdc-filled-button-disabled-container-color: var(--mat-filled-button-disabled-container-color);
  --mdc-filled-button-disabled-label-text-color: var(--mat-filled-button-disabled-label-text-color);
  --mdc-outlined-button-outline-color: var(--mat-outlined-button-outline-color);
  --mdc-outlined-button-label-text-color: var(--mat-outlined-button-label-text-color);
  --mdc-text-button-label-text-color: var(--mat-text-button-label-text-color);

  // ===== 工具栏 =====
  --mat-toolbar-container-background-color: var(--md-sys-color-primary);
  --mat-toolbar-container-text-color: var(--md-sys-color-on-primary);
  --mdc-top-app-bar-container-color: var(--md-sys-color-primary);
  --mdc-top-app-bar-headline-color: var(--md-sys-color-on-primary);

  // ===== 表单控件 =====
  --mat-form-field-focus-color: var(--md-sys-color-primary);
  --mat-form-field-outline-color: var(--md-sys-color-outline);
  --mdc-filled-text-field-container-color: var(--md-sys-color-surface);
  --mdc-filled-text-field-container-shape: var(--md-sys-shape-corner-small);
  --mdc-filled-text-field-focus-active-indicator-color: var(--md-sys-color-primary);
  --mdc-filled-text-field-hover-active-indicator-color: var(--md-sys-color-primary);
  --mdc-filled-text-field-focus-label-text-color: var(--md-sys-color-primary);
  --mdc-filled-text-field-label-text-color: var(--md-sys-color-on-surface-variant);
  --mdc-filled-text-field-input-text-color: var(--md-sys-color-on-surface);
  --mdc-filled-text-field-focus-caret-color: var(--md-sys-color-primary);

  // ===== 卡片 =====
  --mat-card-container-color: var(--md-sys-color-surface);
  --mat-card-subtitle-text-color: var(--md-sys-color-on-surface-variant);
  --mdc-elevated-card-container-color: var(--md-sys-color-surface);
  --mdc-elevated-card-container-shape: var(--md-sys-shape-corner-medium);
  --mdc-outlined-card-container-color: var(--md-sys-color-surface);
  --mdc-outlined-card-container-shape: var(--md-sys-shape-corner-medium);
  --mdc-outlined-card-outline-color: var(--md-sys-color-outline);
  --mdc-outlined-card-outline-width: 1px;

  // ===== 对话框 =====
  --mat-dialog-container-background-color: var(--md-sys-color-surface);
  --mat-dialog-title-text-color: var(--md-sys-color-on-surface);
  --mat-dialog-content-text-color: var(--md-sys-color-on-surface);
  --mdc-dialog-container-color: var(--md-sys-color-surface);
  --mdc-dialog-container-shape: var(--md-sys-shape-corner-large);
  --mdc-dialog-with-divider-divider-color: var(--md-sys-color-outline-variant);
  --mdc-dialog-subhead-color: var(--md-sys-color-on-surface);
  --mdc-dialog-supporting-text-color: var(--md-sys-color-on-surface-variant);

  // ===== 列表 =====
  --mat-list-item-selected-state-layer-color: rgba(var(--md-sys-color-primary-rgb), 0.12);
  --mat-list-item-hover-state-layer-color: rgba(var(--md-sys-color-primary-rgb), 0.08);
  --mdc-list-list-item-container-color: transparent;
  --mdc-list-list-item-selected-container-color: rgba(var(--md-sys-color-primary-rgb), 0.12);
  --mdc-list-list-item-hover-state-layer-color: rgba(var(--md-sys-color-primary-rgb), 0.08);
  --mdc-list-list-item-focus-state-layer-color: rgba(var(--md-sys-color-primary-rgb), 0.12);
  --mdc-list-list-item-selected-state-layer-color: rgba(var(--md-sys-color-primary-rgb), 0.12);
  --mdc-list-list-item-label-text-color: var(--md-sys-color-on-surface);
  --mdc-list-list-item-selected-label-text-color: var(--md-sys-color-primary);

  // ===== 菜单 =====
  --mat-menu-container-color: var(--md-sys-color-surface);
  --mat-menu-item-hover-state-layer-color: rgba(var(--md-sys-color-primary-rgb), 0.08);
  --mdc-menu-container-color: var(--md-sys-color-surface);
  --mdc-menu-container-shape: var(--md-sys-shape-corner-small);
  --mdc-menu-list-item-hover-state-layer-color: rgba(var(--md-sys-color-primary-rgb), 0.08);
  --mdc-menu-list-item-focus-state-layer-color: rgba(var(--md-sys-color-primary-rgb), 0.12);
  --mdc-menu-list-item-label-text-color: var(--md-sys-color-on-surface);

  // ===== 选择器 =====
  --mat-select-enabled-trigger-text-color: var(--md-sys-color-on-surface);
  --mat-select-panel-background-color: var(--md-sys-color-surface);
  --mdc-select-container-color: var(--md-sys-color-surface);
  --mdc-select-container-shape: var(--md-sys-shape-corner-small);
  --mdc-select-focus-active-indicator-color: var(--md-sys-color-primary);
  --mdc-select-hover-active-indicator-color: var(--md-sys-color-primary);
  --mdc-select-focus-label-text-color: var(--md-sys-color-primary);
  --mdc-select-label-text-color: var(--md-sys-color-on-surface-variant);
  --mdc-select-input-text-color: var(--md-sys-color-on-surface);

  // ===== 开关 =====
  --mat-slide-toggle-selected-handle-color: var(--md-sys-color-primary);
  --mat-slide-toggle-selected-track-color: rgba(var(--md-sys-color-primary-rgb), 0.5);
  --mat-slide-toggle-unselected-handle-color: var(--md-sys-color-surface);
  --mat-slide-toggle-unselected-track-color: var(--md-sys-color-outline);
  --mdc-switch-selected-handle-color: var(--md-sys-color-primary);
  --mdc-switch-selected-track-color: rgba(var(--md-sys-color-primary-rgb), 0.5);
  --mdc-switch-unselected-handle-color: var(--md-sys-color-surface);
  --mdc-switch-unselected-track-color: var(--md-sys-color-outline);

  // ===== 标签页 =====
  --mat-tab-header-active-label-text-color: var(--md-sys-color-primary);
  --mat-tab-header-active-ripple-color: var(--md-sys-color-primary);
  --mat-tab-header-inactive-label-text-color: var(--md-sys-color-on-surface-variant);
  --mdc-tab-indicator-active-indicator-color: var(--md-sys-color-primary);
  --mdc-tab-indicator-active-indicator-height: 2px;
  --mdc-tab-active-label-text-color: var(--md-sys-color-primary);
  --mdc-tab-inactive-label-text-color: var(--md-sys-color-on-surface-variant);

  // ===== 复选框 =====
  --mat-checkbox-selected-checkmark-color: var(--md-sys-color-on-primary);
  --mat-checkbox-selected-icon-color: var(--md-sys-color-primary);
  --mdc-checkbox-selected-checkmark-color: var(--md-sys-color-on-primary);
  --mdc-checkbox-selected-icon-color: var(--md-sys-color-primary);

  // ===== 单选按钮 =====
  --mat-radio-checked-icon-color: var(--md-sys-color-primary);
  --mdc-radio-selected-icon-color: var(--md-sys-color-primary);

  // ===== 导航 =====
  --mat-sidenav-container-shape: 0;
}

// ===== 暗色主题覆盖 =====
.dark-theme {
  // 工具栏
  --mat-toolbar-container-background-color: var(--md-sys-color-on-primary-container);
  --mdc-top-app-bar-container-color: var(--md-sys-color-on-primary-container);

  // 卡片和容器
  --mat-card-container-color: var(--md-sys-color-surface);
  --mdc-elevated-card-container-color: var(--md-sys-color-surface);
  --mdc-outlined-card-container-color: var(--md-sys-color-surface);
  --mdc-outlined-card-outline-color: var(--md-sys-color-outline);

  // 对话框
  --mat-dialog-container-background-color: var(--md-sys-color-surface);
  --mdc-dialog-container-color: var(--md-sys-color-surface);
  --mdc-dialog-with-divider-divider-color: var(--md-sys-color-outline);

  // 菜单
  --mat-menu-container-color: var(--md-sys-color-surface);
  --mdc-menu-container-color: var(--md-sys-color-surface);

  // 选择器
  --mat-select-panel-background-color: var(--md-sys-color-surface);
  --mdc-select-container-color: var(--md-sys-color-surface);

  // 表单控件
  --mdc-filled-text-field-container-color: var(--md-sys-color-surface);

  // 开关
  --mdc-switch-unselected-handle-color: var(--md-sys-color-surface-variant);
  --mdc-switch-unselected-track-color: var(--md-sys-color-outline);
}
