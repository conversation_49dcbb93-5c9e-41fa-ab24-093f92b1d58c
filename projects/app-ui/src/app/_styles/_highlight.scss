/* highlight.js GitHub 主题 */
@import 'highlight.js/styles/github.css';

/* 自定义 highlight.js 样式 */
.markdown-content {
  pre {
    position: relative;
    background-color: var(--code-bg-color, #f6f8fa);
    border: var(--border-width-thin, 1px) solid var(--code-border-color, #ddd);
    border-radius: var(--border-radius-md, 6px);
    padding: var(--spacing-md, 16px);
    margin: var(--spacing-md, 16px) 0;
    overflow-x: auto;
  }

  code {
    font-family: var(--font-family-monospace) monospace;
    font-size: var(--font-size-sm, 0.9em);
  }

  pre code {
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    color: var(--code-text-color, #24292e);
    display: block;
    overflow-x: auto;
    line-height: var(--line-height-code, 1.5);
  }

  /* 语言标签样式 */
  pre[data-language]::before {
    content: attr(data-language);
    position: absolute;
    top: 0;
    right: 0;
    padding: var(--spacing-xxs, 2px) var(--spacing-xs, 8px);
    font-size: var(--font-size-xs, 0.75em);
    font-family: var(--font-family-base) sans-serif;
    color: var(--text-secondary, #6a737d);
    background-color: var(--code-tag-bg, rgba(0, 0, 0, 0.05));
    border-radius: 0 0 var(--border-radius-sm, 4px) var(--border-radius-sm, 4px);
    text-transform: uppercase;
  }

  /* 暗色模式适配 */
  .dark-theme & {
    pre {
      background-color: var(--code-bg-color-dark, #0d1117);
      border-color: var(--code-border-color-dark, #30363d);
    }

    pre code {
      color: var(--code-text-color-dark, #c9d1d9);
    }

    pre::before {
      color: var(--text-secondary-dark, #8b949e);
      background-color: var(--code-tag-bg-dark, rgba(255, 255, 255, 0.05));
    }
  }
}

/* 为不同语言添加特定的样式 */
.hljs-keyword {
  color: var(--code-keyword-color, #d73a49);
}

.hljs-string {
  color: var(--code-string-color, #032f62);
}

.hljs-comment {
  color: var(--code-comment-color, #6a737d);
  font-style: italic;
}

.hljs-function {
  color: var(--code-function-color, #6f42c1);
}

.hljs-number {
  color: var(--code-number-color, #005cc5);
}

.hljs-tag {
  color: var(--code-tag-color, #22863a);
}

.hljs-attr {
  color: var(--code-attr-color, #6f42c1);
}

/* 暗色模式下的语法高亮颜色 */
.dark-theme {
  .hljs-keyword {
    color: var(--code-keyword-color-dark, #ff7b72);
  }

  .hljs-string {
    color: var(--code-string-color-dark, #a5d6ff);
  }

  .hljs-comment {
    color: var(--code-comment-color-dark, #8b949e);
  }

  .hljs-function {
    color: var(--code-function-color-dark, #d2a8ff);
  }

  .hljs-number {
    color: var(--code-number-color-dark, #79c0ff);
  }

  .hljs-tag {
    color: var(--code-tag-color-dark, #7ee787);
  }

  .hljs-attr {
    color: var(--code-attr-color-dark, #d2a8ff);
  }
}
