/* KaTeX 样式 */
@import 'katex/dist/katex.min.css';

/* 自定义 KaTeX 样式 */
.katex-error {
  color: var(--error-color, #f44336);
  background-color: var(--error-bg-color, rgba(244, 67, 54, 0.1));
  padding: var(--spacing-xs, 4px) var(--spacing-sm, 8px);
  border-radius: var(--border-radius-sm, 4px);
  font-family: var(--font-family-mono, monospace);
  font-size: var(--font-size-sm, 0.9em);
  white-space: pre-wrap;
  word-break: break-word;
}

/* 调整 KaTeX 在 Markdown 中的显示 */
.markdown-content {
  .katex-display {
    margin: var(--spacing-md, 16px) 0;
    overflow-x: auto;
    overflow-y: hidden;
  }

  .katex {
    font-size: var(--font-size-md, 1em);
  }
}
